"use client";

import Link from "next/link";
import { <PERSON><PERSON> } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import { Card, CardContent } from "@udoy/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogTrigger,
} from "@udoy/components/ui/alert-dialog";
import { MoreHorizontal } from "lucide-react";
import { cn } from "@udoy/utils/shadcn";
import { OrderFullInterface } from "@udoy/utils/types";
import { OrderStatus } from "@prisma/client";
import AssignDeliveryMan from "./AssignDeliveryMan";
import DeleteOrder from "./DeleteOrder";

// Helper function to get status badge variant
function getStatusBadgeVariant(status: string) {
  switch (status) {
    case "DELIVERED":
      return "default";
    case "PROCESSING":
      return "secondary";
    case "PENDING":
      return "outline";
    case "CANCELLED":
      return "destructive";
    case "DELIVERY":
      return "warning";
    case "RETURNED":
      return "destructive";
    default:
      return "outline";
  }
}

// Helper function to format date
function formatDate(date: Date) {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(date);
}

interface OrderCardProps {
  order: OrderFullInterface;
}

export function OrderCard({ order }: OrderCardProps) {
  return (
    <Card className="w-full mb-4">
      <CardContent className="p-4">
        {/* Header with Order ID and Status */}
        <div className="flex justify-between items-start mb-3">
          <div className="flex flex-col gap-1">
            <Link
              href={`/dashboard/orders/${order.id}`}
              className="text-primary hover:underline font-medium"
            >
              #{order.id}
            </Link>
            <span className="text-xs text-muted-foreground">
              {formatDate(order.createdAt)}
            </span>
          </div>
          <Badge
            variant={getStatusBadgeVariant(order.status) as any}
            className={cn(
              order.status === OrderStatus.DELIVERED &&
                "bg-green-700 hover:bg-green-600"
            )}
          >
            {order.status.replace("_", " ")}
          </Badge>
        </div>

        {/* Customer Info */}
        <div className="mb-3">
          <Link href={`/dashboard/customers/${order.buyer.id}`} className="group">
            <p className="font-medium group-hover:underline">{order.buyer.name}</p>
            <p className="text-xs text-muted-foreground">{order.buyer.email}</p>
          </Link>
        </div>

        {/* Order Details Grid */}
        <div className="grid grid-cols-2 gap-3 mb-3 text-sm">
          <div className="flex flex-col">
            <span className="text-muted-foreground text-xs">Items</span>
            <span className="font-medium">{order.orderItems.length}</span>
          </div>
          <div className="flex flex-col">
            <span className="text-muted-foreground text-xs">Total</span>
            <span className="font-medium text-lg">
              ৳ {(order.subTotal + order.shipping).toLocaleString()}
            </span>
          </div>
        </div>

        {/* Delivery Man */}
        <div className="mb-4 p-2 bg-muted/30 rounded-md">
          <span className="text-xs text-muted-foreground">Delivery Man</span>
          <p className="text-sm font-medium">
            {order.deliveryMan?.name ?? "Not Assigned"}
          </p>
          {order.deliveryMan?.email && (
            <p className="text-xs text-muted-foreground">
              {order.deliveryMan.email}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button asChild variant="outline" size="sm" className="flex-1">
            <Link href={`/dashboard/orders/${order.id}`}>View Details</Link>
          </Button>
          
          <AlertDialog>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                  <span className="sr-only">Open menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                
                <DropdownMenuItem asChild>
                  <Link href={`/dashboard/orders/${order.id}`}>View details</Link>
                </DropdownMenuItem>
                <AssignDeliveryMan order={order} />
                <Link href={`/api/orders/${order.id}/invoice`} target="_blank">
                  <DropdownMenuItem>Get Invoice</DropdownMenuItem>
                </Link>
                <DropdownMenuSeparator />
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem className="bg-destructive text-destructive-foreground mt-2">
                    Delete Order
                  </DropdownMenuItem>
                </AlertDialogTrigger>
              </DropdownMenuContent>
            </DropdownMenu>
            <DeleteOrder id={order.id} />
          </AlertDialog>
        </div>
      </CardContent>
    </Card>
  );
}
