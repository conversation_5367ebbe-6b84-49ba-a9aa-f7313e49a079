import { Suspense } from "react";
import { getPrisma } from "@udoy/utils/db-utils";
import { OrderFilters } from "./components/OrderFilters";
import { buildQuery, FilterParams } from "./utils/queryBuilder";
import { Role } from "@prisma/client";
import Loading from "./loading";
import SyncDeliveryMen from "./components/SyncDeliveryMen";
import { OrdersPagination } from "./components/OrdersPagination";
import { ResponsiveOrdersTable } from "./components/ResponsiveOrdersTable";



export default async function PageWrapper({
  searchParams,
}: {
  searchParams: Promise<FilterParams>;
}) {
  const params = await searchParams;

  return (
    <Suspense key={JSON.stringify(params)} fallback={<Loading />}>
      <OrdersPage searchParams={params} />
    </Suspense>
  );
}

async function OrdersPage({ searchParams }: { searchParams: FilterParams }) {
  const prisma = getPrisma();
  const { whereClause, orderByClause, page, limit, skip, ...filterProps } =
    buildQuery(searchParams);

  // Get total count for pagination
  const totalCount = await prisma.order.count({
    where: whereClause,
  });

  // Fetch orders with filters, sorting, and pagination
  const orders = await prisma.order.findMany({
    where: whereClause,
    orderBy: orderByClause,
    skip,
    take: limit,
    include: {
      buyer: true,
      address: true,
      deliveryMan: true,
      orderItems: {
        include: {
          product: {
            include: {
              images: true,
            },
          },
          picker: true,
        },
      },
    },
  });

  const deliveryMen = await prisma.user.findMany({
    where: {
      role: {
        in: [Role.DELIVERY_MAN, Role.SUPER_ADMIN, Role.ADMIN],
      },
    },

    include: {
      _count: {
        select: {
          deliveries: true,
        },
      },
    },
  });

  // Calculate pagination info
  const totalPages = Math.ceil(totalCount / limit);
  const currentPage = page;
  const hasNextPage = currentPage < totalPages;
  const hasPrevPage = currentPage > 1;

  const paginationInfo = {
    totalCount,
    totalPages,
    currentPage,
    hasNextPage,
    hasPrevPage,
    limit,
  };

  return (
    <div className="flex flex-col gap-6 sm:p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Orders</h1>
      </div>

      <OrderFilters {...filterProps} />

      <SyncDeliveryMen users={deliveryMen}>
        <ResponsiveOrdersTable orders={orders} />
        <OrdersPagination paginationInfo={paginationInfo} />
      </SyncDeliveryMen>
    </div>
  );
}
